{"level":"info","message":"OpenRouter client initialized successfully","service":"litellm-claude-router","timestamp":"2025-08-04T03:03:00.512Z"}
{"level":"info","message":"🚀 LiteLLM Claude Router started on http://127.0.0.1:3456","service":"litellm-claude-router","timestamp":"2025-08-04T03:03:00.536Z"}
{"level":"info","message":"📋 Available models: openrouter/horizon-beta, z-ai/glm-4.5-air:free, qwen/qwen3-coder:free, moonshotai/kimi-k2:free","service":"litellm-claude-router","timestamp":"2025-08-04T03:03:00.537Z"}
{"level":"info","message":"🎯 Default model: qwen/qwen3-coder:free","service":"litellm-claude-router","timestamp":"2025-08-04T03:03:00.538Z"}
{"level":"info","message":"OpenRouter client initialized successfully","service":"litellm-claude-router","timestamp":"2025-08-04T03:04:45.567Z"}
{"level":"info","message":"🚀 LiteLLM Claude Router started on http://127.0.0.1:3456","service":"litellm-claude-router","timestamp":"2025-08-04T03:04:45.594Z"}
{"level":"info","message":"📋 Available models: openrouter/horizon-beta, z-ai/glm-4.5-air:free, qwen/qwen3-coder:free, moonshotai/kimi-k2:free","service":"litellm-claude-router","timestamp":"2025-08-04T03:04:45.595Z"}
{"level":"info","message":"🎯 Default model: qwen/qwen3-coder:free","service":"litellm-claude-router","timestamp":"2025-08-04T03:04:45.596Z"}
{"level":"info","message":"OpenRouter client initialized successfully","service":"litellm-claude-router","timestamp":"2025-08-04T03:05:54.746Z"}
{"level":"info","message":"🚀 LiteLLM Claude Router started on http://127.0.0.1:3456","service":"litellm-claude-router","timestamp":"2025-08-04T03:05:54.767Z"}
{"level":"info","message":"📋 Available models: openrouter/horizon-beta, z-ai/glm-4.5-air:free, qwen/qwen3-coder:free, moonshotai/kimi-k2:free","service":"litellm-claude-router","timestamp":"2025-08-04T03:05:54.769Z"}
{"level":"info","message":"🎯 Default model: qwen/qwen3-coder:free","service":"litellm-claude-router","timestamp":"2025-08-04T03:05:54.769Z"}
{"level":"info","message":"OpenRouter client initialized successfully","service":"litellm-claude-router","timestamp":"2025-08-04T03:28:04.450Z"}
{"level":"info","message":"🚀 LiteLLM Claude Router started on http://127.0.0.1:3456","service":"litellm-claude-router","timestamp":"2025-08-04T03:28:04.473Z"}
{"level":"info","message":"📋 Available models: openrouter/horizon-beta, z-ai/glm-4.5-air:free, qwen/qwen3-coder:free, moonshotai/kimi-k2:free","service":"litellm-claude-router","timestamp":"2025-08-04T03:28:04.474Z"}
{"level":"info","message":"🎯 Default model: qwen/qwen3-coder:free","service":"litellm-claude-router","timestamp":"2025-08-04T03:28:04.475Z"}
{"level":"info","message":"GET /health - 127.0.0.1","service":"litellm-claude-router","timestamp":"2025-08-04T03:28:34.378Z"}
{"level":"info","message":"POST /v1/chat/completions - 127.0.0.1","service":"litellm-claude-router","timestamp":"2025-08-04T03:31:02.624Z"}
{"level":"info","message":"Chat completion request for model: qwen/qwen3-coder:free","service":"litellm-claude-router","timestamp":"2025-08-04T03:31:02.626Z"}
{"level":"info","message":"OpenRouter Request: POST /chat/completions","service":"litellm-claude-router","timestamp":"2025-08-04T03:31:02.630Z"}
{"level":"error","message":"OpenRouter Response Error:","service":"litellm-claude-router","timestamp":"2025-08-04T03:31:03.298Z"}
{"cause":{"code":"ETIMEDOUT"},"code":"ETIMEDOUT","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"https://openrouter.ai/api/v1","data":"{\"model\":\"qwen/qwen3-coder:free\",\"messages\":[{\"role\":\"user\",\"content\":\"??,????????\"}]}","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer 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","Content-Length":"86","Content-Type":"application/json","HTTP-Referer":"http://localhost:3456","User-Agent":"axios/1.11.0","X-Title":"LiteLLM Claude Router"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":60000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/chat/completions","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"errors":[{"address":"************","code":"ETIMEDOUT","errno":-4039,"port":443,"syscall":"connect"},{"address":"2606:4700::6812:373","code":"ENETUNREACH","errno":-4062,"port":443,"syscall":"connect"},{"address":"************","code":"ETIMEDOUT","errno":-4039,"port":443,"syscall":"connect"},{"address":"2606:4700::6812:273","code":"ENETUNREACH","errno":-4062,"port":443,"syscall":"connect"}],"level":"error","message":"Chat completion failed:","name":"AggregateError","request":{"_currentRequest":{"_closed":false,"_contentLength":"86","_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /api/v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer 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\r\nHTTP-Referer: http://localhost:3456\r\nX-Title: LiteLLM Claude Router\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 86\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: openrouter.ai\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":false,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{"openrouter.ai:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null,null,null],"connect":[null,null,null],"end":[null,null],"newListener":[null,null],"timeout":[null,null,null]},"_eventsCount":11,"_hadError":true,"_host":"openrouter.ai","_httpMessage":"[Circular]","_newSessionPending":false,"_parent":null,"_pendingData":[{"chunk":"POST /api/v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer 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\r\nHTTP-Referer: http://localhost:3456\r\nX-Title: LiteLLM Claude Router\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 86\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: openrouter.ai\r\nConnection: keep-alive\r\n\r\n","encoding":"latin1"},{"chunk":{"data":[123,34,109,111,100,101,108,34,58,34,113,119,101,110,47,113,119,101,110,51,45,99,111,100,101,114,58,102,114,101,101,34,44,34,109,101,115,115,97,103,101,115,34,58,91,123,34,114,111,108,101,34,58,34,117,115,101,114,34,44,34,99,111,110,116,101,110,116,34,58,34,63,63,44,63,63,63,63,63,63,63,63,34,125,93,125],"type":"Buffer"},"encoding":"buffer"}],"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":false,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":506,"pendingcb":1,"writelen":506},"allowHalfOpen":false,"alpnProtocol":null,"authorizationError":null,"authorized":false,"autoSelectFamilyAttemptedAddresses":["************:443","2606:4700::6812:373:443","************:443","2606:4700::6812:273:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":false},"_parentWrap":null,"_secureContext":{"context":{}},"reading":false},"parser":null,"secureConnecting":true,"servername":null,"ssl":null,"timeout":60000}]},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":false,"finished":false,"host":"openrouter.ai","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/api/v1/chat/completions","protocol":"https:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"socket":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null,null,null],"connect":[null,null,null],"end":[null,null],"newListener":[null,null],"timeout":[null,null,null]},"_eventsCount":11,"_hadError":true,"_host":"openrouter.ai","_httpMessage":"[Circular]","_newSessionPending":false,"_parent":null,"_pendingData":[{"chunk":"POST /api/v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer 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\r\nHTTP-Referer: http://localhost:3456\r\nX-Title: LiteLLM Claude Router\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 86\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: openrouter.ai\r\nConnection: keep-alive\r\n\r\n","encoding":"latin1"},{"chunk":{"data":[123,34,109,111,100,101,108,34,58,34,113,119,101,110,47,113,119,101,110,51,45,99,111,100,101,114,58,102,114,101,101,34,44,34,109,101,115,115,97,103,101,115,34,58,91,123,34,114,111,108,101,34,58,34,117,115,101,114,34,44,34,99,111,110,116,101,110,116,34,58,34,63,63,44,63,63,63,63,63,63,63,63,34,125,93,125],"type":"Buffer"},"encoding":"buffer"}],"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":false,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":506,"pendingcb":1,"writelen":506},"allowHalfOpen":false,"alpnProtocol":null,"authorizationError":null,"authorized":false,"autoSelectFamilyAttemptedAddresses":["************:443","2606:4700::6812:373:443","************:443","2606:4700::6812:273:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":false},"_parentWrap":null,"_secureContext":{"context":{}},"reading":false},"parser":null,"secureConnecting":true,"servername":null,"ssl":null,"timeout":60000},"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"_currentUrl":"https://openrouter.ai/api/v1/chat/completions","_ended":false,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer 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","Content-Length":"86","Content-Type":"application/json","HTTP-Referer":"http://localhost:3456","User-Agent":"axios/1.11.0","X-Title":"LiteLLM Claude Router"},"hostname":"openrouter.ai","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{"openrouter.ai:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null,null,null],"connect":[null,null,null],"end":[null,null],"newListener":[null,null],"timeout":[null,null,null]},"_eventsCount":11,"_hadError":true,"_host":"openrouter.ai","_httpMessage":{"_closed":false,"_contentLength":"86","_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /api/v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer 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\r\nHTTP-Referer: http://localhost:3456\r\nX-Title: LiteLLM Claude Router\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 86\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: openrouter.ai\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":false,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":false,"host":"openrouter.ai","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/api/v1/chat/completions","protocol":"https:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"socket":"[Circular]","strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"_newSessionPending":false,"_parent":null,"_pendingData":[{"chunk":"POST /api/v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer 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\r\nHTTP-Referer: http://localhost:3456\r\nX-Title: LiteLLM Claude Router\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 86\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: openrouter.ai\r\nConnection: keep-alive\r\n\r\n","encoding":"latin1"},{"chunk":{"data":[123,34,109,111,100,101,108,34,58,34,113,119,101,110,47,113,119,101,110,51,45,99,111,100,101,114,58,102,114,101,101,34,44,34,109,101,115,115,97,103,101,115,34,58,91,123,34,114,111,108,101,34,58,34,117,115,101,114,34,44,34,99,111,110,116,101,110,116,34,58,34,63,63,44,63,63,63,63,63,63,63,63,34,125,93,125],"type":"Buffer"},"encoding":"buffer"}],"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":false,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":506,"pendingcb":1,"writelen":506},"allowHalfOpen":false,"alpnProtocol":null,"authorizationError":null,"authorized":false,"autoSelectFamilyAttemptedAddresses":["************:443","2606:4700::6812:373:443","************:443","2606:4700::6812:273:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":false},"_parentWrap":null,"_secureContext":{"context":{}},"reading":false},"parser":null,"secureConnecting":true,"servername":null,"ssl":null,"timeout":60000}]},"totalSocketCount":1}}},"path":"/api/v1/chat/completions","pathname":"/api/v1/chat/completions","port":"","protocol":"https:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[{"data":{"data":[123,34,109,111,100,101,108,34,58,34,113,119,101,110,47,113,119,101,110,51,45,99,111,100,101,114,58,102,114,101,101,34,44,34,109,101,115,115,97,103,101,115,34,58,91,123,34,114,111,108,101,34,58,34,117,115,101,114,34,44,34,99,111,110,116,101,110,116,34,58,34,63,63,44,63,63,63,63,63,63,63,63,34,125,93,125],"type":"Buffer"}}],"_requestBodyLength":86,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"service":"litellm-claude-router","stack":"AggregateError\n    at AxiosError.from (file:///X:/src/powernewdefault/litellm-claude-router/node_modules/axios/lib/core/AxiosError.js:92:14)\n    at RedirectableRequest.handleRequestError (file:///X:/src/powernewdefault/litellm-claude-router/node_modules/axios/lib/adapters/http.js:620:25)\n    at RedirectableRequest.emit (node:events:531:35)\n    at eventHandlers.<computed> (X:\\src\\powernewdefault\\litellm-claude-router\\node_modules\\follow-redirects\\index.js:49:24)\n    at ClientRequest.emit (node:events:519:28)\n    at TLSSocket.socketErrorListener (node:_http_client:500:9)\n    at TLSSocket.emit (node:events:519:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (file:///X:/src/powernewdefault/litellm-claude-router/node_modules/axios/lib/core/Axios.js:45:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async OpenRouterClient.chatCompletion (file:///X:/src/powernewdefault/litellm-claude-router/openrouter-client.js:79:24)\n    at async file:///X:/src/powernewdefault/litellm-claude-router/server.js:160:26","timestamp":"2025-08-04T03:31:03.301Z"}
{"cause":{"code":"ETIMEDOUT"},"code":"ETIMEDOUT","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"https://openrouter.ai/api/v1","data":"{\"model\":\"qwen/qwen3-coder:free\",\"messages\":[{\"role\":\"user\",\"content\":\"??,????????\"}]}","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer 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","Content-Length":"86","Content-Type":"application/json","HTTP-Referer":"http://localhost:3456","User-Agent":"axios/1.11.0","X-Title":"LiteLLM Claude Router"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeout":60000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/chat/completions","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"errors":[{"address":"************","code":"ETIMEDOUT","errno":-4039,"port":443,"syscall":"connect"},{"address":"2606:4700::6812:373","code":"ENETUNREACH","errno":-4062,"port":443,"syscall":"connect"},{"address":"************","code":"ETIMEDOUT","errno":-4039,"port":443,"syscall":"connect"},{"address":"2606:4700::6812:273","code":"ENETUNREACH","errno":-4062,"port":443,"syscall":"connect"}],"level":"error","message":"Chat completion error:","name":"AggregateError","request":{"_currentRequest":{"_closed":false,"_contentLength":"86","_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /api/v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer 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\r\nHTTP-Referer: http://localhost:3456\r\nX-Title: LiteLLM Claude Router\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 86\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: openrouter.ai\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":false,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{"openrouter.ai:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null,null,null],"connect":[null,null,null],"end":[null,null],"newListener":[null,null],"timeout":[null,null,null]},"_eventsCount":11,"_hadError":true,"_host":"openrouter.ai","_httpMessage":"[Circular]","_newSessionPending":false,"_parent":null,"_pendingData":[{"chunk":"POST /api/v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer 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\r\nHTTP-Referer: http://localhost:3456\r\nX-Title: LiteLLM Claude Router\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 86\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: openrouter.ai\r\nConnection: keep-alive\r\n\r\n","encoding":"latin1"},{"chunk":{"data":[123,34,109,111,100,101,108,34,58,34,113,119,101,110,47,113,119,101,110,51,45,99,111,100,101,114,58,102,114,101,101,34,44,34,109,101,115,115,97,103,101,115,34,58,91,123,34,114,111,108,101,34,58,34,117,115,101,114,34,44,34,99,111,110,116,101,110,116,34,58,34,63,63,44,63,63,63,63,63,63,63,63,34,125,93,125],"type":"Buffer"},"encoding":"buffer"}],"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":false,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":506,"pendingcb":1,"writelen":506},"allowHalfOpen":false,"alpnProtocol":null,"authorizationError":null,"authorized":false,"autoSelectFamilyAttemptedAddresses":["************:443","2606:4700::6812:373:443","************:443","2606:4700::6812:273:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":false},"_parentWrap":null,"_secureContext":{"context":{}},"reading":false},"parser":null,"secureConnecting":true,"servername":null,"ssl":null,"timeout":60000}]},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":false,"finished":false,"host":"openrouter.ai","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/api/v1/chat/completions","protocol":"https:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"socket":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null,null,null],"connect":[null,null,null],"end":[null,null],"newListener":[null,null],"timeout":[null,null,null]},"_eventsCount":11,"_hadError":true,"_host":"openrouter.ai","_httpMessage":"[Circular]","_newSessionPending":false,"_parent":null,"_pendingData":[{"chunk":"POST /api/v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer 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\r\nHTTP-Referer: http://localhost:3456\r\nX-Title: LiteLLM Claude Router\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 86\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: openrouter.ai\r\nConnection: keep-alive\r\n\r\n","encoding":"latin1"},{"chunk":{"data":[123,34,109,111,100,101,108,34,58,34,113,119,101,110,47,113,119,101,110,51,45,99,111,100,101,114,58,102,114,101,101,34,44,34,109,101,115,115,97,103,101,115,34,58,91,123,34,114,111,108,101,34,58,34,117,115,101,114,34,44,34,99,111,110,116,101,110,116,34,58,34,63,63,44,63,63,63,63,63,63,63,63,34,125,93,125],"type":"Buffer"},"encoding":"buffer"}],"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":false,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":506,"pendingcb":1,"writelen":506},"allowHalfOpen":false,"alpnProtocol":null,"authorizationError":null,"authorized":false,"autoSelectFamilyAttemptedAddresses":["************:443","2606:4700::6812:373:443","************:443","2606:4700::6812:273:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":false},"_parentWrap":null,"_secureContext":{"context":{}},"reading":false},"parser":null,"secureConnecting":true,"servername":null,"ssl":null,"timeout":60000},"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"_currentUrl":"https://openrouter.ai/api/v1/chat/completions","_ended":false,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer 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","Content-Length":"86","Content-Type":"application/json","HTTP-Referer":"http://localhost:3456","User-Agent":"axios/1.11.0","X-Title":"LiteLLM Claude Router"},"hostname":"openrouter.ai","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{"openrouter.ai:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null,null,null],"connect":[null,null,null],"end":[null,null],"newListener":[null,null],"timeout":[null,null,null]},"_eventsCount":11,"_hadError":true,"_host":"openrouter.ai","_httpMessage":{"_closed":false,"_contentLength":"86","_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /api/v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer 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\r\nHTTP-Referer: http://localhost:3456\r\nX-Title: LiteLLM Claude Router\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 86\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: openrouter.ai\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":false,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":false,"host":"openrouter.ai","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/api/v1/chat/completions","protocol":"https:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"socket":"[Circular]","strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"_newSessionPending":false,"_parent":null,"_pendingData":[{"chunk":"POST /api/v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer 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\r\nHTTP-Referer: http://localhost:3456\r\nX-Title: LiteLLM Claude Router\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 86\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: openrouter.ai\r\nConnection: keep-alive\r\n\r\n","encoding":"latin1"},{"chunk":{"data":[123,34,109,111,100,101,108,34,58,34,113,119,101,110,47,113,119,101,110,51,45,99,111,100,101,114,58,102,114,101,101,34,44,34,109,101,115,115,97,103,101,115,34,58,91,123,34,114,111,108,101,34,58,34,117,115,101,114,34,44,34,99,111,110,116,101,110,116,34,58,34,63,63,44,63,63,63,63,63,63,63,63,34,125,93,125],"type":"Buffer"},"encoding":"buffer"}],"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":false,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":506,"pendingcb":1,"writelen":506},"allowHalfOpen":false,"alpnProtocol":null,"authorizationError":null,"authorized":false,"autoSelectFamilyAttemptedAddresses":["************:443","2606:4700::6812:373:443","************:443","2606:4700::6812:273:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":false},"_parentWrap":null,"_secureContext":{"context":{}},"reading":false},"parser":null,"secureConnecting":true,"servername":null,"ssl":null,"timeout":60000}]},"totalSocketCount":1}}},"path":"/api/v1/chat/completions","pathname":"/api/v1/chat/completions","port":"","protocol":"https:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[{"data":{"data":[123,34,109,111,100,101,108,34,58,34,113,119,101,110,47,113,119,101,110,51,45,99,111,100,101,114,58,102,114,101,101,34,44,34,109,101,115,115,97,103,101,115,34,58,91,123,34,114,111,108,101,34,58,34,117,115,101,114,34,44,34,99,111,110,116,101,110,116,34,58,34,63,63,44,63,63,63,63,63,63,63,63,34,125,93,125],"type":"Buffer"}}],"_requestBodyLength":86,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"service":"litellm-claude-router","stack":"AggregateError\n    at AxiosError.from (file:///X:/src/powernewdefault/litellm-claude-router/node_modules/axios/lib/core/AxiosError.js:92:14)\n    at RedirectableRequest.handleRequestError (file:///X:/src/powernewdefault/litellm-claude-router/node_modules/axios/lib/adapters/http.js:620:25)\n    at RedirectableRequest.emit (node:events:531:35)\n    at eventHandlers.<computed> (X:\\src\\powernewdefault\\litellm-claude-router\\node_modules\\follow-redirects\\index.js:49:24)\n    at ClientRequest.emit (node:events:519:28)\n    at TLSSocket.socketErrorListener (node:_http_client:500:9)\n    at TLSSocket.emit (node:events:519:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (file:///X:/src/powernewdefault/litellm-claude-router/node_modules/axios/lib/core/Axios.js:45:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async OpenRouterClient.chatCompletion (file:///X:/src/powernewdefault/litellm-claude-router/openrouter-client.js:79:24)\n    at async file:///X:/src/powernewdefault/litellm-claude-router/server.js:160:26","timestamp":"2025-08-04T03:31:03.319Z"}
{"level":"info","message":"OpenRouter client initialized successfully","service":"litellm-claude-router","timestamp":"2025-08-04T03:31:39.185Z"}
{"level":"info","message":"🚀 LiteLLM Claude Router started on http://127.0.0.1:3456","service":"litellm-claude-router","timestamp":"2025-08-04T03:31:39.207Z"}
{"level":"info","message":"📋 Available models: openrouter/horizon-beta, z-ai/glm-4.5-air:free, qwen/qwen3-coder:free, moonshotai/kimi-k2:free","service":"litellm-claude-router","timestamp":"2025-08-04T03:31:39.208Z"}
{"level":"info","message":"🎯 Default model: qwen/qwen3-coder:free","service":"litellm-claude-router","timestamp":"2025-08-04T03:31:39.209Z"}
{"level":"info","message":"OpenRouter client initialized successfully","service":"litellm-claude-router","timestamp":"2025-08-04T03:38:37.415Z"}
{"level":"info","message":"🚀 LiteLLM Claude Router started on http://127.0.0.1:3456","service":"litellm-claude-router","timestamp":"2025-08-04T03:38:37.438Z"}
{"level":"info","message":"📋 Available models: openrouter/horizon-beta, z-ai/glm-4.5-air:free, qwen/qwen3-coder:free, moonshotai/kimi-k2:free","service":"litellm-claude-router","timestamp":"2025-08-04T03:38:37.439Z"}
{"level":"info","message":"🎯 Default model: qwen/qwen3-coder:free","service":"litellm-claude-router","timestamp":"2025-08-04T03:38:37.440Z"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"litellm-claude-router","timestamp":"2025-08-04T03:39:30.425Z"}
{"level":"info","message":"OpenRouter client initialized successfully","service":"litellm-claude-router","timestamp":"2025-08-04T03:40:03.510Z"}
{"level":"info","message":"🚀 LiteLLM Claude Router started on http://127.0.0.1:3456","service":"litellm-claude-router","timestamp":"2025-08-04T03:40:03.532Z"}
{"level":"info","message":"📋 Available models: openrouter/horizon-beta, z-ai/glm-4.5-air:free, qwen/qwen3-coder:free, moonshotai/kimi-k2:free","service":"litellm-claude-router","timestamp":"2025-08-04T03:40:03.534Z"}
{"level":"info","message":"🎯 Default model: qwen/qwen3-coder:free","service":"litellm-claude-router","timestamp":"2025-08-04T03:40:03.535Z"}
{"level":"info","message":"OpenRouter client initialized successfully","service":"litellm-claude-router","timestamp":"2025-08-04T03:46:42.331Z"}
{"level":"info","message":"🚀 LiteLLM Claude Router started on http://127.0.0.1:3456","service":"litellm-claude-router","timestamp":"2025-08-04T03:46:42.352Z"}
{"level":"info","message":"📋 Available models: openrouter/horizon-beta, z-ai/glm-4.5-air:free, qwen/qwen3-coder:free, moonshotai/kimi-k2:free","service":"litellm-claude-router","timestamp":"2025-08-04T03:46:42.353Z"}
{"level":"info","message":"🎯 Default model: qwen/qwen3-coder:free","service":"litellm-claude-router","timestamp":"2025-08-04T03:46:42.354Z"}
